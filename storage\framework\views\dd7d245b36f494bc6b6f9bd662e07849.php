<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LaporinAja - Bantu Perbaiki Kampung Kita</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-50 text-gray-800 font-sans">

    <!-- Navbar -->
    <nav class="absolute top-0 left-0 w-full z-50 backdrop-blur bg-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-6">

            <!-- Logo -->
             <div class="flex items-center space-x-2">
            <?php if (isset($component)) { $__componentOriginal8892e718f3d0d7a916180885c6f012e7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8892e718f3d0d7a916180885c6f012e7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.application-logo','data' => ['class' => 'w-10 h-10']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('application-logo'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-10 h-10']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8892e718f3d0d7a916180885c6f012e7)): ?>
<?php $attributes = $__attributesOriginal8892e718f3d0d7a916180885c6f012e7; ?>
<?php unset($__attributesOriginal8892e718f3d0d7a916180885c6f012e7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8892e718f3d0d7a916180885c6f012e7)): ?>
<?php $component = $__componentOriginal8892e718f3d0d7a916180885c6f012e7; ?>
<?php unset($__componentOriginal8892e718f3d0d7a916180885c6f012e7); ?>
<?php endif; ?>
        </div>

            <!-- Menu Desktop -->
            <div class="hidden md:flex space-x-8 items-center">
                <a href="#fitur" class="text-blue-400 hover:text-white transition font-medium">Fitur</a>
                <a href="#statistik" class="text-blue-400 hover:text-white transition font-medium">Statistik</a>
                <a href="#panduan" class="text-blue-400 hover:text-white transition font-medium">Panduan</a>
                <a href="#tentang" class="text-blue-400 hover:text-white transition font-medium">Tentang</a>
                <a href="#callcenter" class="text-blue-400 hover:text-white transition font-medium">Call Center</a>
            </div>
            <div class="hidden md:flex space-x-4 items-center">
                <!-- Aksi -->
                <a href="<?php echo e(route('login')); ?>" class="text-white hover:text-blue-700 transition font-medium">Login</a>
                <a href="<?php echo e(route('register')); ?>"
                    class="px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-semibold">Daftar</a>
            </div>

            <!-- Burger Menu Mobile -->
            <div class="md:hidden flex items-center">
                <button id="mobileMenuBtn" class="text-white focus:outline-none">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Menu Mobile -->
        <div id="mobileMenu" class="md:hidden hidden px-4 pb-4 space-y-2 bg-blue-700/80 text-white backdrop-blur">
            <a href="#fitur" class="block hover:text-blue-200">Fitur</a>
            <a href="#statistik" class="block hover:text-blue-200">Statistik</a>
            <a href="#panduan" class="block hover:text-blue-200">Panduan</a>
            <a href="#tentang" class="block hover:text-blue-200">Tentang</a>
            <a href="#callcenter" class="block hover:text-blue-200">Call center</a>
            <hr class="border-blue-200">
            <a href="<?php echo e(route('login')); ?>" class="block font-medium">Login</a>
            <a href="<?php echo e(route('register')); ?>"
                class="block bg-blue-600 text-white px-4 py-2 rounded mt-2 text-center">Daftar</a>
        </div>
    </nav>




    <!-- Hero Section -->
    <section
        class="relative bg-gradient-to-r from-blue-100 to-blue-300 text-white min-h-[90vh] flex items-center justify-center pt-24">
        <div class="max-w-4xl mx-auto px-4">
            <h1 class="text-5xl sm:text-4x1 font-extrabold mb-6 text-blue-800">Bersama Wujudkan Kampung Nyaman & Aman
            </h1>
            <p class="text-xl sm:text-1xl text-gray-800 mb-8">
                LaporinAja memudahkan warga menyampaikan keluhan, memantau progres, dan memastikan tindakan cepat di
                lingkungan kita.
            </p>
            <a href="<?php echo e(route('register')); ?>"
                class="inline-block bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold shadow hover:bg-blue-900 transition">
                Daftar & Mulai Lapor
            </a>
        </div>
    </section>

    <!-- Fitur Section -->
    <section id="fitur" class="py-20 bg-white">
        <div class="max-w-6xl mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-12 text-blue-800">Kenapa Harus LaporinAja?</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
                <div class="bg-blue-50 p-8 rounded-xl shadow hover:shadow-lg transition">
                    <h3 class="text-xl font-semibold text-blue-700 mb-4">Laporan Cepat & Mudah</h3>
                    <p class="text-gray-700 mb-4">Kirim laporan hanya dalam hitungan menit melalui aplikasi atau website
                        kami.</p>
                    <img src="https://cdn-icons-png.flaticon.com/512/3050/3050121.png" class="mx-auto h-20"
                        alt="Icon">
                </div>
                <div class="bg-blue-50 p-8 rounded-xl shadow hover:shadow-lg transition">
                    <h3 class="text-xl font-semibold text-blue-700 mb-4">Pantau Progres Real-Time</h3>
                    <p class="text-gray-700 mb-4">Lihat perkembangan laporan secara transparan dari awal hingga selesai.
                    </p>
                    <img src="https://cdn-icons-png.flaticon.com/512/4149/4149647.png" class="mx-auto h-20"
                        alt="Icon">
                </div>
                <div class="bg-blue-50 p-8 rounded-xl shadow hover:shadow-lg transition">
                    <h3 class="text-xl font-semibold text-blue-700 mb-4">Tanggapan Profesional</h3>
                    <p class="text-gray-700 mb-4">Petugas terlatih akan menangani laporan dengan sigap dan terukur.</p>
                    <img src="https://cdn-icons-png.flaticon.com/512/3208/3208724.png" class="mx-auto h-20"
                        alt="Icon">
                </div>
            </div>
        </div>
    </section>

    <!-- Statistik Section -->
    <section id="statistik" class="py-20 bg-gradient-to-r from-blue-50 to-blue-100">
        <div class="max-w-6xl mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold text-blue-800 mb-12">Statistik Singkat</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">

                <div
                    class="p-6 bg-white rounded-xl shadow text-center transform transition hover:scale-105 hover:shadow-xl">
                    <h3 class="text-4xl font-bold text-blue-700 mb-2">+12,000</h3>
                    <p class="text-gray-700">Laporan Terkirim</p>
                </div>

                <div
                    class="p-6 bg-white rounded-xl shadow text-center transform transition hover:scale-105 hover:shadow-xl">
                    <h3 class="text-4xl font-bold text-blue-700 mb-2">98%</h3>
                    <p class="text-gray-700">Laporan Tuntas</p>
                </div>

                <div
                    class="p-6 bg-white rounded-xl shadow text-center transform transition hover:scale-105 hover:shadow-xl">
                    <h3 class="text-4xl font-bold text-blue-700 mb-2">+5,000</h3>
                    <p class="text-gray-700">Warga Terdaftar</p>
                </div>

            </div>
        </div>
    </section>

    <!-- Panduan Section -->
    <section id="panduan" class="py-20 bg-gray-50">
        <div class="max-w-6xl mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-12 text-blue-800">Panduan Penggunaan LaporinAja</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">

                <div class="bg-white p-6 rounded-xl shadow hover:shadow-lg transition text-left">
                    <h3 class="text-lg font-semibold text-blue-700 mb-2">1. Registrasi Akun</h3>
                    <p class="text-gray-700 mb-3">Daftarkan diri Anda melalui website dengan mengisi data diri dan
                        membuat akun LaporinAja.</p>
                    <img src="https://cdn-icons-png.flaticon.com/512/456/456212.png" class="mx-auto h-20"
                        alt="Registrasi">
                </div>

                <div class="bg-white p-6 rounded-xl shadow hover:shadow-lg transition text-left">
                    <h3 class="text-lg font-semibold text-blue-700 mb-2">2. Buat Laporan</h3>
                    <p class="text-gray-700 mb-3">Pilih kategori masalah, tulis deskripsi, unggah foto bukti, dan
                        tandai lokasi kejadian.</p>
                    <img src="https://cdn-icons-png.flaticon.com/512/747/747376.png" class="mx-auto h-20"
                        alt="Lapor">
                </div>

                <div class="bg-white p-6 rounded-xl shadow hover:shadow-lg transition text-left">
                    <h3 class="text-lg font-semibold text-blue-700 mb-2">3. Pantau Progres</h3>
                    <p class="text-gray-700 mb-3">Lihat status laporan Anda secara real-time, mulai dari diproses
                        hingga selesai.</p>
                    <img src="https://cdn-icons-png.flaticon.com/512/3135/3135715.png" class="mx-auto h-20"
                        alt="Pantau">
                </div>

                <div class="bg-white p-6 rounded-xl shadow hover:shadow-lg transition text-left">
                    <h3 class="text-lg font-semibold text-blue-700 mb-2">4. Dapatkan Notifikasi</h3>
                    <p class="text-gray-700 mb-3">Kami akan mengirimkan pemberitahuan ke akun Anda jika ada update
                        terkait laporan.</p>
                    <img src="https://cdn-icons-png.flaticon.com/512/1827/1827392.png" class="mx-auto h-20"
                        alt="Notifikasi">
                </div>

                <div class="bg-white p-6 rounded-xl shadow hover:shadow-lg transition text-left">
                    <h3 class="text-lg font-semibold text-blue-700 mb-2">5. Berikan Penilaian</h3>
                    <p class="text-gray-700 mb-3">Setelah laporan selesai ditangani, Anda dapat memberikan rating dan
                        komentar terhadap layanan.</p>
                    <img src="https://cdn-icons-png.flaticon.com/512/833/833472.png" class="mx-auto h-20"
                        alt="Penilaian">
                </div>

                <div class="bg-white p-6 rounded-xl shadow hover:shadow-lg transition text-left">
                    <h3 class="text-lg font-semibold text-blue-700 mb-2">6. Wujudkan Kampung Nyaman</h3>
                    <p class="text-gray-700 mb-3">Bersama-sama kita ciptakan lingkungan yang lebih aman, nyaman, dan
                        tertata.</p>
                    <img src="https://cdn-icons-png.flaticon.com/512/3306/3306864.png" class="mx-auto h-20"
                        alt="Lingkungan Nyaman">
                </div>

            </div>
        </div>
    </section>


    <!-- Tentang Section -->
    <section id="tentang" class="py-20 bg-white">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold text-blue-800 mb-6">Tentang Kami</h2>
            <p class="text-gray-700 text-lg mb-4">
                LaporinAja hadir sebagai solusi digital untuk menjembatani komunikasi warga dengan pengelola kampung.
                Dengan aplikasi ini, permasalahan bisa dilaporkan tanpa harus ribet, dan progres penanganannya dapat
                dipantau oleh semua pihak.
            </p>
            <p class="text-gray-700 text-lg">
                Visi kami adalah menciptakan kampung yang lebih baik, aman, dan nyaman dengan peran aktif dari seluruh
                masyarakat.
            </p>
        </div>
    </section>


    <!-- Call to Action -->
    <section class="py-20 bg-blue-700 text-white text-center">
        <div class="max-w-4xl mx-auto px-4">
            <h2 class="text-3xl sm:text-4xl font-bold mb-6">Ayo Jadi Bagian dari Kampung yang Lebih Baik!</h2>
            <p class="text-lg mb-6">
                Lingkungan aman, bersih, dan nyaman bukan hanya harapan — tapi bisa jadi kenyataan jika kita bergerak
                bersama. LaporinAja hadir untuk memudahkan warga dalam menyampaikan permasalahan kampung tanpa ribet.
            </p>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 text-left max-w-3xl mx-auto mb-8">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2l4 -4m5 2a9 9 0 11-18 0a9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-bold text-lg">Laporan Langsung Ditindak</h3>
                        <p class="text-sm">Setiap laporan akan diteruskan ke petugas yang berwenang tanpa menunggu
                            lama.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-bold text-lg">Pantau Progres Secara Real-Time</h3>
                        <p class="text-sm">Lihat perkembangan laporan Anda mulai dari verifikasi hingga selesai
                            penanganan.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16a8 8 0 000 16z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-bold text-lg">Lebih Aman & Terorganisir</h3>
                        <p class="text-sm">Laporan hanya bisa diakses oleh petugas resmi, data Anda aman & terlindungi.
                        </p>
                    </div>
                </div>

                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M7 8h10M7 12h4m1 8a9 9 0 100-18a9 9 0 000 18z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-bold text-lg">Mudah Digunakan Siapa Saja</h3>
                        <p class="text-sm">Tampilan sederhana, cocok untuk semua usia. Tinggal klik, laporan langsung
                            terkirim.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- Footer -->
    <footer class="bg-gray-900 text-gray-300 pt-10">
        <section id="callcenter">
            <div class="max-w-2xl mx-auto px-4 text-center pb-10 border-b border-gray-700">
                <h2 class="text-2xl font-bold text-white mb-2">Hubungi Call Center</h2>
                <p class="text-gray-400 mb-4">Jika Anda membutuhkan bantuan cepat, hubungi kami atau kirim pesan
                    melalui form di bawah ini.</p>

                <div class="text-white mb-6 space-y-2">
                    <p>📞 <strong>+62 812-3456-7890</strong></p>
                    <p>📧 <strong><EMAIL></strong></p>
                </div>

                <div class="flex justify-center max-w-xl mx-auto w-full space-x-2">
                    <form action="<?php echo e(route('feedback.callcenter.store')); ?>" method="POST"
                        class="flex flex-col w-full space-y-2">
                        <?php echo csrf_field(); ?>

                        <input type="text" name="nama" placeholder="Nama Anda" required
                            class="w-full px-4 py-2 rounded bg-white text-black focus:outline-blue-500">

                        <input type="email" name="email" placeholder="Email Anda" required
                            class="w-full px-4 py-2 rounded bg-white text-black focus:outline-blue-500">

                        <input type="text" name="pesan" placeholder="Tulis pesan atau pertanyaan Anda..."
                            required class="w-full px-4 py-2 rounded bg-white text-black focus:outline-blue-500">

                        <button type="submit"
                            class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition">Kirim</button>
                    </form>
                </div>
            </div>
        </section>

        <!-- Copyright -->
        <div class="text-center text-gray-500 text-sm py-6 border-t border-gray-800">
            &copy; <?php echo e(date('Y')); ?> LaporinAja. Dibuat oleh Sikami untuk Warga. Seluruh Hak Cipta Dilindungi.
        </div>

    </footer>

    <script>
        const mobileBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');

        mobileBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
    </script>


</body>

</html>
<?php /**PATH C:\xampp\htdocs\web\LaporinAja\resources\views/landing.blade.php ENDPATH**/ ?>