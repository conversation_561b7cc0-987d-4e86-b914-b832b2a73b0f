@extends('dashboard.template')

@section('title', 'Detail Laporan')

@section('content')
    <h1 class="text-2xl font-bold mb-6">Detail Laporan</h1>

    <div class="bg-white shadow rounded p-6 space-y-4">

        {{-- Judul --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">Judul</h2>
            <p class="text-gray-700">{{ $laporan->judul }}</p>
        </div>

        {{-- Kategori --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">Kategori</h2>
            <p class="text-gray-700">{{ $laporan->kategori }}</p>
        </div>

        {{-- Lokasi --}}
        <div>
            <h2 class="font-semibold text-lg mb-3">📍 Lokasi</h2>
            <div class="text-gray-700 space-y-3">
                <p><strong>Alamat:</strong> {{ $laporan->alamat ?? 'Tidak ada alamat' }}</p>
                <p><strong>RT/RW:</strong> {{ $laporan->rt ?? '-' }} / {{ $laporan->rw ?? '-' }}</p>

                @if ($laporan->latitude && $laporan->longitude)
                    <div class="bg-gray-50 p-4 rounded-lg border">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-medium text-gray-800">🗺️ Peta Lokasi</h3>
                            <a href="https://www.google.com/maps/dir/?api=1&destination={{ $laporan->latitude }},{{ $laporan->longitude }}"
                                target="_blank"
                                class="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Navigasi ke Lokasi
                            </a>
                        </div>

                        <div id="detail-map" class="w-full h-64 border rounded-lg bg-gray-100 mb-3"></div>

                        <div class="mt-3 grid grid-cols-2 gap-4 text-sm">
                            <div class="bg-white p-2 rounded border">
                                <span class="text-gray-600">Latitude:</span>
                                <span class="font-mono">{{ number_format($laporan->latitude, 6) }}</span>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <span class="text-gray-600">Longitude:</span>
                                <span class="font-mono">{{ number_format($laporan->longitude, 6) }}</span>
                            </div>
                        </div>
                    </div>

                    @push('scripts')
                        <script async defer
                            src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.key') }}&callback=initDetailMap"
                            onerror="handleDetailMapError()">
                        </script>

                        <script>
                            function handleDetailMapError() {
                                console.error('Google Maps gagal dimuat.');
                                const mapContainer = document.getElementById('detail-map');
                                if (mapContainer) {
                                    mapContainer.innerHTML = `<div class="p-4 text-center text-red-600">Gagal memuat Google Maps.</div>`;
                                }
                            }

                            function initDetailMap() {
                                try {
                                    const location = {
                                        lat: {{ $laporan->latitude }},
                                        lng: {{ $laporan->longitude }}
                                    };

                                    const map = new google.maps.Map(document.getElementById('detail-map'), {
                                        zoom: 15,
                                        center: location,
                                        disableDefaultUI: true
                                    });

                                    new google.maps.Marker({
                                        position: location,
                                        map: map,
                                        animation: google.maps.Animation.DROP
                                    });

                                } catch (e) {
                                    console.error('Error initDetailMap:', e);
                                    handleDetailMapError();
                                }
                            }
                        </script>
                    @endpush
                @else
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <p class="text-yellow-800 text-sm flex items-center">
                            Koordinat lokasi tidak tersedia untuk laporan ini.
                        </p>
                    </div>
                @endif
            </div>
        </div>

        {{-- Isi Laporan --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">Isi Laporan</h2>
            <p class="text-gray-700">{{ $laporan->isi }}</p>
        </div>

        {{-- Status --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">Status</h2>
            <span
                class="inline-block px-3 py-1 rounded text-white
                @if ($laporan->status == 'belum_diproses') bg-gray-500
                @elseif($laporan->status == 'diproses') bg-yellow-500
                @else bg-green-600 @endif">
                {{ ucfirst($laporan->status) }}
            </span>
        </div>

        {{-- Prioritas --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">Prioritas</h2>
            <div class="flex items-center space-x-3">
                <span
                    class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
                    @if ($laporan->prioritas == 'tinggi') bg-red-100 text-red-800
                    @elseif($laporan->prioritas == 'sedang') bg-yellow-100 text-yellow-800
                    @else bg-green-100 text-green-800 @endif">
                    @if ($laporan->prioritas == 'tinggi')
                        🔴 Prioritas Tinggi
                    @elseif($laporan->prioritas == 'sedang')
                        🟡 Prioritas Sedang
                    @else
                        🟢 Prioritas Rendah
                    @endif
                </span>

                @if (in_array(auth()->user()->role, ['admin', 'petugas']))
                    <form action="{{ route('laporan.prioritas', $laporan->id) }}" method="POST" class="inline-block">
                        @csrf
                        <select name="prioritas" onchange="this.form.submit()"
                            class="text-xs border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500">
                            <option value="rendah" {{ $laporan->prioritas == 'rendah' ? 'selected' : '' }}>Rendah</option>
                            <option value="sedang" {{ $laporan->prioritas == 'sedang' ? 'selected' : '' }}>Sedang</option>
                            <option value="tinggi" {{ $laporan->prioritas == 'tinggi' ? 'selected' : '' }}>Tinggi</option>
                        </select>
                    </form>
                @endif
            </div>
        </div>

        {{-- Pelapor --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">Pelapor</h2>
            <p class="text-gray-700">{{ $laporan->user->name ?? '-' }}</p>
        </div>

        {{-- Tanggal dan Waktu Dibuat --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">📅 Tanggal & Waktu Dibuat</h2>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex items-center">
                        <div class="bg-blue-100 p-2 rounded-lg mr-3">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Tanggal</p>
                            <p class="font-semibold text-gray-900">{{ $laporan->created_at->format('d F Y') }}</p>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="bg-blue-100 p-2 rounded-lg mr-3">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Waktu</p>
                            <p class="font-semibold text-gray-900">{{ $laporan->created_at->format('H:i') }} WIB</p>
                        </div>
                    </div>
                </div>
                <div class="mt-3 pt-3 border-t border-blue-200">
                    <p class="text-xs text-gray-500">
                        <span class="font-medium">Dibuat:</span> {{ $laporan->created_at->diffForHumans() }}
                        @if($laporan->updated_at != $laporan->created_at)
                            | <span class="font-medium">Terakhir diupdate:</span> {{ $laporan->updated_at->diffForHumans() }}
                        @endif
                    </p>
                </div>
            </div>
        </div>

        {{-- Bukti Awal --}}
        @if ($laporan->bukti_awal)
            <div>
                <h2 class="font-semibold text-lg mb-3">Bukti Kondisi Awal</h2>
                <img src="{{ asset('storage/' . $laporan->bukti_awal) }}"
                    class="w-full max-w-md rounded-lg shadow-md border" alt="Bukti Awal">
            </div>
        @endif

        {{-- Bukti Penyelesaian atau Laporan --}}
        @if ($laporan->bukti)
    <div>
        <h2 class="font-semibold text-lg mb-3">
            {{ auth()->user()->role == 'user' ? 'Bukti Penyelesaian' : 'Bukti Laporan dari User' }}
        </h2>
        <img src="{{ asset('storage/' . $laporan->bukti) }}" class="w-full max-w-md rounded-lg shadow-md border" alt="Bukti">

        @if (in_array(auth()->user()->role, ['admin', 'petugas']))
            <a href="{{ asset('storage/' . $laporan->bukti) }}" download
                                class="inline-flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Download Bukti
                            </a>
        @endif
    </div>
@endif

        {{-- Tombol Kembali --}}
        <div class="mt-6">
            @php
                $user = auth()->user();
                $backRoute = '#';
                $backText = '← Kembali';

                if ($user->role === 'admin' || $user->role === 'petugas') {
                    $backRoute = route('laporan.index');
                    $backText = '← Kembali ke Laporan';
                } elseif ($user->role === 'user') {
                    $backRoute = route('user.dashboard');
                    $backText = '← Kembali ke Dashboard';
                }
            @endphp

            <a href="{{ $backRoute }}" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                {{ $backText }}
            </a>
        </div>


        {{-- Rating (Hanya untuk User & Status selesai & Belum ngasih rating) --}}
        @if (auth()->user()->role == 'user' && $laporan->status == 'selesai' && !$laporan->rating)
            <div class="mt-6 bg-white shadow rounded p-6">
                <h2 class="font-semibold text-lg mb-4">Beri Rating Laporan Ini</h2>
                <form action="{{ route('rating.store') }}" method="POST" class="space-y-4">
                    @csrf
                    <input type="hidden" name="laporan_id" value="{{ $laporan->id }}">

                    <div>
                        <label for="rating" class="block font-medium mb-1">Rating (1 - 5 Bintang)</label>
                        <select name="rating" required class="w-full border rounded px-3 py-2">
                            <option value="">Pilih Bintang</option>
                            <option value="1">1 - Sangat Buruk</option>
                            <option value="2">2 - Buruk</option>
                            <option value="3">3 - Cukup</option>
                            <option value="4">4 - Baik</option>
                            <option value="5">5 - Sangat Baik</option>
                        </select>
                    </div>

                    <div>
                        <label for="komentar" class="block font-medium mb-1">Komentar (Opsional)</label>
                        <textarea name="komentar" rows="3" class="w-full border rounded px-3 py-2" placeholder="Tulis komentar Anda..."></textarea>
                    </div>

                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Kirim
                        Rating</button>
                </form>
            </div>
        @endif

    </div>
@endsection
@if ($laporan->latitude && $laporan->longitude)
@section('scripts')
    <!-- Check if Google Maps API key is configured -->
    @if (env('GOOGLE_MAPS_API_KEY') && env('GOOGLE_MAPS_API_KEY') !== 'AIzaSyA_kWxBOtirWjmaZKt4wCww3u8zwojaKtU')
        <!-- Google Maps JavaScript API -->
        <script async defer
            src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&callback=initDetailMap"
            onerror="handleDetailMapError()"></script>
    @else
        <!-- No valid API key, show fallback immediately -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                showDetailMapError();
            });
        </script>
    @endif

    <script>
        // Error handling for Google Maps API
        function handleDetailMapError() {
            console.error('Google Maps API failed to load');
            showDetailMapError();
        }

        function showDetailMapError() {
            const mapContainer = document.getElementById('detail-map');
            if (mapContainer) {
                mapContainer.innerHTML = `
                    <div class="flex items-center justify-center h-full bg-red-50 border-2 border-red-200 rounded-lg">
                        <div class="text-center p-6">
                            <svg class="w-12 h-12 text-red-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-red-800 mb-2">Peta Tidak Tersedia</h3>
                            <p class="text-sm text-red-600 mb-3">Google Maps tidak dapat dimuat.</p>
                            <a href="https://www.google.com/maps/dir/?api=1&destination={{ $laporan->latitude }},{{ $laporan->longitude }}"
                               target="_blank"
                               class="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                Buka di Google Maps
                            </a>
                        </div>
                    </div>
                `;
            }
        }

        function initDetailMap() {
            try {
                // Check if Google Maps is available
                if (typeof google === 'undefined' || !google.maps) {
                    throw new Error('Google Maps API not loaded');
                }

                const location = {
                    lat: {{ $laporan->latitude }},
                    lng: {{ $laporan->longitude }}
                };

                const map = new google.maps.Map(document.getElementById('detail-map'), {
                    zoom: 15,
                    center: location,
                    mapTypeControl: true,
                    streetViewControl: true,
                    fullscreenControl: true,
                    zoomControl: true,
                    styles: [
                        {
                            featureType: 'poi',
                            elementType: 'labels',
                            stylers: [{ visibility: 'on' }]
                        }
                    ]
                });

                const marker = new google.maps.Marker({
                    position: location,
                    map: map,
                    animation: google.maps.Animation.DROP,
                    title: '{{ $laporan->judul }}',
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="#dc2626">
                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(32, 32),
                        anchor: new google.maps.Point(16, 32)
                    }
                });

                // Add info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div class="p-2">
                            <h3 class="font-semibold text-sm mb-1">{{ $laporan->judul }}</h3>
                            <p class="text-xs text-gray-600 mb-1">{{ $laporan->kategori }}</p>
                            <p class="text-xs text-gray-500">{{ $laporan->alamat ?? 'Alamat tidak tersedia' }}</p>
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    infoWindow.open(map, marker);
                });

                // Auto-open info window
                setTimeout(() => {
                    infoWindow.open(map, marker);
                }, 1000);

            } catch (error) {
                console.error('Error initializing detail map:', error);
                showDetailMapError();
            }
        }
    </script>
@endsection
@endif