@extends('dashboard.template')

@section('title', 'Detail Laporan')

@section('content')
    <h1 class="text-2xl font-bold mb-6">Detail Laporan</h1>

    <div class="bg-white shadow rounded p-6 space-y-4">

        {{-- Judul --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">Judul</h2>
            <p class="text-gray-700">{{ $laporan->judul }}</p>
        </div>

        {{-- Kategori --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">Kategori</h2>
            <p class="text-gray-700">{{ $laporan->kategori }}</p>
        </div>

        {{-- Lokasi --}}
        <div>
            <h2 class="font-semibold text-lg mb-3">📍 Lokasi</h2>
            <div class="text-gray-700 space-y-3">
                <p><strong>Alamat:</strong> {{ $laporan->alamat ?? 'Tidak ada alamat' }}</p>
                <p><strong>RT/RW:</strong> {{ $laporan->rt ?? '-' }} / {{ $laporan->rw ?? '-' }}</p>

                @if ($laporan->latitude && $laporan->longitude)
                    <div class="bg-gray-50 p-4 rounded-lg border">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-medium text-gray-800">🗺️ Peta Lokasi</h3>
                            <a href="https://www.google.com/maps/dir/?api=1&destination={{ $laporan->latitude }},{{ $laporan->longitude }}"
                                target="_blank"
                                class="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition">
                                Navigasi ke Lokasi
                            </a>
                        </div>

                        <div id="detail-map" class="w-full h-64 border rounded-lg bg-gray-100"></div>

                        <div class="mt-3 grid grid-cols-2 gap-4 text-sm">
                            <div class="bg-white p-2 rounded border">
                                <span class="text-gray-600">Latitude:</span>
                                <span class="font-mono">{{ number_format($laporan->latitude, 6) }}</span>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <span class="text-gray-600">Longitude:</span>
                                <span class="font-mono">{{ number_format($laporan->longitude, 6) }}</span>
                            </div>
                        </div>
                    </div>

                    @push('scripts')
                        <script async defer
                            src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&callback=initDetailMap"></script>

                        <script>
                            function initDetailMap() {
                                const location = {
                                    lat: {{ $laporan->latitude }},
                                    lng: {{ $laporan->longitude }}
                                };
                                const map = new google.maps.Map(document.getElementById('detail-map'), {
                                    zoom: 15,
                                    center: location,
                                    disableDefaultUI: true
                                });
                                new google.maps.Marker({
                                    position: location,
                                    map,
                                    animation: google.maps.Animation.DROP
                                });
                            }
                        </script>
                    @endpush
                @else
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <p class="text-yellow-800 text-sm flex items-center">
                            Koordinat lokasi tidak tersedia untuk laporan ini.
                        </p>
                    </div>
                @endif
            </div>
        </div>

        {{-- Isi Laporan --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">Isi Laporan</h2>
            <p class="text-gray-700">{{ $laporan->isi }}</p>
        </div>

        {{-- Status --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">Status</h2>
            <span
                class="inline-block px-3 py-1 rounded text-white
                @if ($laporan->status == 'belum_diproses') bg-gray-500
                @elseif($laporan->status == 'diproses') bg-yellow-500
                @else bg-green-600 @endif">
                {{ ucfirst($laporan->status) }}
            </span>
        </div>

        {{-- Prioritas --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">Prioritas</h2>
            <div class="flex items-center space-x-3">
                <span
                    class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
                    @if ($laporan->prioritas == 'tinggi') bg-red-100 text-red-800
                    @elseif($laporan->prioritas == 'sedang') bg-yellow-100 text-yellow-800
                    @else bg-green-100 text-green-800 @endif">
                    @if ($laporan->prioritas == 'tinggi')
                        🔴 Prioritas Tinggi
                    @elseif($laporan->prioritas == 'sedang')
                        🟡 Prioritas Sedang
                    @else
                        🟢 Prioritas Rendah
                    @endif
                </span>

                @if (in_array(auth()->user()->role, ['admin', 'petugas']))
                    <form action="{{ route('laporan.prioritas', $laporan->id) }}" method="POST" class="inline-block">
                        @csrf
                        <select name="prioritas" onchange="this.form.submit()"
                            class="text-xs border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500">
                            <option value="rendah" {{ $laporan->prioritas == 'rendah' ? 'selected' : '' }}>Rendah</option>
                            <option value="sedang" {{ $laporan->prioritas == 'sedang' ? 'selected' : '' }}>Sedang</option>
                            <option value="tinggi" {{ $laporan->prioritas == 'tinggi' ? 'selected' : '' }}>Tinggi</option>
                        </select>
                    </form>
                @endif
            </div>
        </div>

        {{-- Pelapor --}}
        <div>
            <h2 class="font-semibold text-lg mb-1">Pelapor</h2>
            <p class="text-gray-700">{{ $laporan->user->name ?? '-' }}</p>
        </div>

        {{-- Bukti Awal --}}
        @if ($laporan->bukti_awal)
            <div>
                <h2 class="font-semibold text-lg mb-3">Bukti Kondisi Awal</h2>
                <img src="{{ asset('storage/' . $laporan->bukti_awal) }}"
                    class="w-full max-w-md rounded-lg shadow-md border" alt="Bukti Awal">
            </div>
        @endif

        {{-- Bukti Penyelesaian atau Laporan --}}
        @if ($laporan->bukti)
    <div>
        <h2 class="font-semibold text-lg mb-3">
            {{ auth()->user()->role == 'user' ? 'Bukti Penyelesaian' : 'Bukti Laporan dari User' }}
        </h2>
        <img src="{{ asset('storage/' . $laporan->bukti) }}" class="w-full max-w-md rounded-lg shadow-md border" alt="Bukti">

        @if (in_array(auth()->user()->role, ['admin', 'petugas']))
            <a href="{{ asset('storage/' . $laporan->bukti) }}" download
                                class="inline-flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Download Bukti
                            </a>
        @endif
    </div>
@endif

        {{-- Tombol Kembali --}}
        <div class="mt-6">
            @php
                $user = auth()->user();
                $backRoute = '#';
                $backText = '← Kembali';

                if ($user->role === 'admin' || $user->role === 'petugas') {
                    $backRoute = route('laporan.index');
                    $backText = '← Kembali ke Laporan';
                } elseif ($user->role === 'user') {
                    $backRoute = route('user.dashboard');
                    $backText = '← Kembali ke Dashboard';
                }
            @endphp

            <a href="{{ $backRoute }}" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                {{ $backText }}
            </a>
        </div>


        {{-- Rating (Hanya untuk User & Status selesai & Belum ngasih rating) --}}
        @if (auth()->user()->role == 'user' && $laporan->status == 'selesai' && !$laporan->rating)
            <div class="mt-6 bg-white shadow rounded p-6">
                <h2 class="font-semibold text-lg mb-4">Beri Rating Laporan Ini</h2>
                <form action="{{ route('rating.store') }}" method="POST" class="space-y-4">
                    @csrf
                    <input type="hidden" name="laporan_id" value="{{ $laporan->id }}">

                    <div>
                        <label for="rating" class="block font-medium mb-1">Rating (1 - 5 Bintang)</label>
                        <select name="rating" required class="w-full border rounded px-3 py-2">
                            <option value="">Pilih Bintang</option>
                            <option value="1">1 - Sangat Buruk</option>
                            <option value="2">2 - Buruk</option>
                            <option value="3">3 - Cukup</option>
                            <option value="4">4 - Baik</option>
                            <option value="5">5 - Sangat Baik</option>
                        </select>
                    </div>

                    <div>
                        <label for="komentar" class="block font-medium mb-1">Komentar (Opsional)</label>
                        <textarea name="komentar" rows="3" class="w-full border rounded px-3 py-2" placeholder="Tulis komentar Anda..."></textarea>
                    </div>

                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Kirim
                        Rating</button>
                </form>
            </div>
        @endif

    </div>
@endsection
