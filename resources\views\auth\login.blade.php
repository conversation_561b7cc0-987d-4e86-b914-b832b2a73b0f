<x-guest-layout>
    <div class="flex items-center justify-center bg-gray-100 min-h-[200px] py-7">
        <div class="w-full max-w-md bg-white bg-opacity-90 backdrop-blur-md p-8 rounded-xl shadow-lg z-10">

            <!-- Judul -->
            <h1 class="text-2xl font-bold text-black mb-6 text-center">Login</h1>

            <!-- Notifikasi -->
            @if (session('status'))
                <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg text-sm text-center">
                    {{ session('status') }}
                </div>
            @endif

            <!-- Form -->
            <form method="POST" action="{{ route('login') }}" class="space-y-4">
                @csrf

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                    <input id="email" type="email" name="email" value="{{ old('email') }}" required autofocus
                           autocomplete="username"
                           class="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500"/>
                    <x-input-error :messages="$errors->get('email')" class="mt-2"/>
                </div>

                <!-- Password -->
                <div class="relative">
                    <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                    <input id="password" type="password" name="password" required autocomplete="current-password"
                           class="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500"/>

                    <!-- Toggle icon -->
                    <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 top-[34px] pr-3 flex items-center">
                        <svg id="eyeIcon" class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path id="eyePath" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z
                                     M2.458 12C3.732 7.943 7.523 5 12 5
                                     c4.478 0 8.268 2.943 9.542 7
                                     -1.274 4.057-5.064 7-9.542 7
                                     -4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                    </button>
                    <x-input-error :messages="$errors->get('password')" class="mt-2"/>
                </div>

                <!-- Remember -->
                <div class="flex items-center">
                    <input id="remember_me" type="checkbox" name="remember"
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="remember_me" class="ml-2 block text-sm text-gray-700">Remember me</label>
                </div>

                <!-- Tombol Login -->
                <div>
                    <button type="submit"
                            class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                        Login
                    </button>
                </div>

                <!-- Link Daftar -->
                <p class="text-sm text-center mt-4">
                    Belum punya akun?
                    <a href="{{ route('register') }}" class="text-blue-600 hover:underline">Daftar</a>
                </p>

                <!-- Lupa Password -->
                @if (Route::has('password.request'))
                    <p class="text-sm text-center mt-2">
                        <a class="text-blue-600 hover:underline" href="{{ route('password.request') }}">
                            Lupa password?
                        </a>
                    </p>
                @endif

                <!-- Tombol Kembali (di bawah) -->
                <div class="text-center mt-6">
                    <a href="{{ url('/') }}" class="inline-flex items-center text-gray-600 hover:text-blue-700 transition text-sm">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                        </svg>
                        Kembali ke Beranda
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Script Toggle -->
    <script>
        const togglePassword = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('password');
        const eyePath = document.getElementById('eyePath');

        togglePassword.addEventListener('click', function () {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            eyePath.setAttribute('d',
                type === 'password'
                    ? "M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5 c4.478 0 8.268 2.943 9.542 7 -1.274 4.057-5.064 7-9.542 7 -4.477 0-8.268-2.943-9.542-7z"
                    : "M3 3l18 18M9.88 9.88a3 3 0 104.24 4.24M21 21L3 3"
            );
        });
    </script>
</x-guest-layout>
