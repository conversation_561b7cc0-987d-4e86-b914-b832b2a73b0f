<?php $__env->startSection('title', 'Data Feedback'); ?>

<?php $__env->startSection('content'); ?>
<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">Data Feedback Pengguna</h1>

    <?php if(auth()->user()->role === 'petugas'): ?>
        <a href="<?php echo e(route('dashboard.petugas')); ?>"
           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow transition duration-200 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Kembali ke Dashboard
        </a>
    <?php elseif(auth()->user()->role === 'admin'): ?>
        <a href="<?php echo e(route('admin.dashboard')); ?>"
           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow transition duration-200 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Kembali ke Dashboard
        </a>
    <?php endif; ?>
</div>

<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <table class="w-full">
        <thead>
            <tr class="bg-blue-600 text-white">
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">#</th>
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Nama</th>
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Email</th>
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Role</th>
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Pesan</th>
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Rating</th>
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Waktu</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            <?php $__empty_1 = true; $__currentLoopData = $feedbacks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $feedback): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    <?php echo e($key + 1); ?>

                </td>
                <td class="px-4 py-4">
                    <div class="text-sm font-medium text-gray-900"><?php echo e($feedback->user->name ?? $feedback->nama ?? '-'); ?></div>
                </td>
                <td class="px-4 py-4 text-sm text-gray-900">
                    <?php echo e($feedback->user->email ?? $feedback->email ?? '-'); ?>

                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                        <?php echo e($feedback->role == 'admin' ? 'bg-red-100 text-red-800' :
                           ($feedback->role == 'petugas' ? 'bg-yellow-100 text-yellow-800' :
                           ($feedback->role == 'user' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-700'))); ?>">
                        <?php echo e(ucfirst($feedback->role)); ?>

                    </span>
                </td>
                <td class="px-4 py-4 text-sm text-gray-900">
                    <?php echo e($feedback->pesan); ?>

                </td>
                <td class="px-4 py-4 text-yellow-500">
                    <?php for($i = 1; $i <= 5; $i++): ?>
                        <?php if($i <= $feedback->rating): ?>
                            <span>★</span>
                        <?php else: ?>
                            <span class="text-gray-300">★</span>
                        <?php endif; ?>
                    <?php endfor; ?>
                </td>
                <td class="px-4 py-4 text-sm text-gray-500">
                    <?php echo e($feedback->created_at->format('d-m-Y H:i')); ?>

                </td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <tr>
                <td colspan="7" class="px-4 py-8 text-center">
                    <div class="flex flex-col items-center">
                        <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="text-gray-500 text-lg font-medium">Belum ada feedback.</p>
                    </div>
                </td>
            </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('dashboard.template', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\web\LaporinAja\resources\views/feedback/index.blade.php ENDPATH**/ ?>