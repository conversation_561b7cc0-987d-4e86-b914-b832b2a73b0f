<?php $__env->startSection('title', 'Proses <PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<h1 class="text-2xl font-bold mb-4">Proses <PERSON></h1>

<div class="p-6 bg-white shadow rounded-lg">
    <form action="<?php echo e(url('/laporan/'.$laporan->id.'/proses')); ?>" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

        <div class="mb-4">
            <label class="block font-semibold mb-1"><PERSON><PERSON><PERSON> (Foto/Video)</label>
            <input type="file" name="bukti_awal" required class="border p-2 w-full rounded" accept="image/*,video/*">
            <?php $__errorArgs = ['bukti_awal'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <p class="text-red-500 text-sm"><?php echo e($message); ?></p>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="mb-4">
            <label class="block font-semibold mb-1">Catatan / Deskripsi Proses</label>
            <textarea name="catatan_proses" rows="4" class="border p-2 w-full rounded" placeholder="Tulis catatan proses atau keterangan tambahan..."></textarea>
            <?php $__errorArgs = ['catatan_proses'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <p class="text-red-500 text-sm"><?php echo e($message); ?></p>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="flex gap-2">
            <button type="submit" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                Tandai Diproses
            </button>
            <a href="<?php echo e(url()->previous()); ?>" class="inline-block px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition">
                ← Kembali
            </a>
        </div>
    </form>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('dashboard.template', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\web\LaporinAja\resources\views/laporan/proses.blade.php ENDPATH**/ ?>