<?php $__env->startSection('title', $title); ?>

<?php $__env->startSection('content'); ?>


<div class="flex justify-between items-center mb-6">
    <div>
        <h1 class="text-2xl font-bold text-gray-800">Dashboard Admin</h1>
        <p class="text-sm text-gray-600">Terakhir diperbarui: <?php echo e(now()->format('d/m/Y H:i:s')); ?></p>
    </div>
    <button onclick="window.location.reload()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow flex items-center space-x-2">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        <span>Refresh Data</span>
    </button>
</div>


<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
    <?php $__currentLoopData = $cards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <a href="<?php echo e($card['url']); ?>" class="flex items-center space-x-4 p-5 bg-<?php echo e($card['color']); ?>-100 rounded-xl shadow hover:shadow-lg hover:scale-[1.02] transition transform">
            <div class="p-3 bg-<?php echo e($card['color']); ?>-200 rounded-full">
                <?php switch($card['title']):
                    case ('Total Laporan'): ?>
                        <svg class="w-6 h-6 text-<?php echo e($card['color']); ?>-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M9 17v-6h13V7H9V3L2 12l7 9z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <?php break; ?>
                    <?php case ('Belum Diproses'): ?>
                        <svg class="w-6 h-6 text-<?php echo e($card['color']); ?>-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M12 8v4l3 3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="12" r="10" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <?php break; ?>
                    <?php case ('Diproses'): ?>
                        <svg class="w-6 h-6 text-<?php echo e($card['color']); ?>-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M4 4h16v16H4z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <?php break; ?>
                    <?php case ('Selesai'): ?>
                        <svg class="w-6 h-6 text-<?php echo e($card['color']); ?>-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M5 13l4 4L19 7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <?php break; ?>
                    <?php case ('Total Pengguna'): ?>
                        <svg class="w-6 h-6 text-<?php echo e($card['color']); ?>-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M17 20h5V4H2v16h5m10 0v-6H7v6h10zM7 12h10M7 16h10" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <?php break; ?>
                    <?php case ('Pembuatan Akun Petugas'): ?>
                        <svg class="w-6 h-6 text-<?php echo e($card['color']); ?>-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M12 4v16m8-8H4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <?php break; ?>
                <?php endswitch; ?>
            </div>
            <div>
                <h2 class="text-lg font-bold text-<?php echo e($card['color']); ?>-800"><?php echo e($card['title']); ?></h2>
                <p class="text-gray-700 mt-1 font-medium"><?php echo e($card['desc']); ?></p>
            </div>
        </a>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>


<h2 class="text-xl font-bold mb-4">Grafik Laporan per Bulan</h2>
<canvas id="laporanChart" height="100"></canvas>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    const ctx = document.getElementById('laporanChart').getContext('2d');
    const laporanChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: <?php echo json_encode($labels, 15, 512) ?>,
            datasets: [{
                label: 'Total Laporan',
                data: <?php echo json_encode($data, 15, 512) ?>,
                backgroundColor: 'rgba(54, 162, 235, 0.7)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true,
                    precision: 0
                }
            }
        }
    });
</script>


<div class="bg-white p-6 rounded-lg shadow">
    <h3 class="text-lg font-bold mb-4">Laporan Terbaru</h3>
    <?php if($laporanTerbaru->count() > 0): ?>
        <div class="overflow-x-auto">
            <table class="min-w-full text-left text-sm">
                <thead>
                    <tr class="bg-gray-100 text-gray-700">
                        <th class="py-2 px-4">Judul</th>
                        <th class="py-2 px-4">Kategori</th>
                        <th class="py-2 px-4">Status</th>
                        <th class="py-2 px-4">Tanggal</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $laporanTerbaru; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $laporan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="border-b">
                            <td class="py-2 px-4"><?php echo e($laporan->judul); ?></td>
                            <td class="py-2 px-4"><?php echo e($laporan->kategori ?? '-'); ?></td>
                            <td class="py-2 px-4">
                                <span class="px-2 py-1 rounded-full text-xs <?php echo e($laporan->status == 'selesai' ? 'bg-green-200 text-green-800' :
                                    ($laporan->status == 'diproses' ? 'bg-yellow-200 text-yellow-800' : 'bg-gray-200 text-gray-800')); ?>">
                                    <?php echo e(ucfirst($laporan->status)); ?>

                                </span>
                            </td>
                            <td class="py-2 px-4"><?php echo e($laporan->created_at->format('d-m-Y')); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p class="text-gray-500">Belum ada laporan terbaru.</p>
    <?php endif; ?>
</div>


<div class="bg-white p-6 rounded-lg shadow mt-6">
    <h3 class="text-lg font-bold mb-4">Feedback Terbaru</h3>
    <?php if($feedbackTerbaru->count() > 0): ?>
        <div class="overflow-x-auto">
            <table class="min-w-full text-left text-sm">
                <thead>
                    <tr class="bg-gray-100 text-gray-700">
                        <th class="py-2 px-4">Nama</th>
                        <th class="py-2 px-4">Pesan</th>
                        <th class="py-2 px-4">Rating</th>
                        <th class="py-2 px-4">Tanggal</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $feedbackTerbaru; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feedback): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="border-b">
                            <td class="py-2 px-4"><?php echo e($feedback->user->nama ?? $feedback->nama ?? '-'); ?></td>
                            <td class="py-2 px-4"><?php echo e($feedback->pesan); ?></td>
                            <td class="py-2 px-4">
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <?php if($i <= $feedback->rating): ?>
                                        <span class="text-yellow-400">★</span>
                                    <?php else: ?>
                                        <span class="text-gray-300">★</span>
                                    <?php endif; ?>
                                <?php endfor; ?>
                            </td>
                            <td class="py-2 px-4"><?php echo e($feedback->created_at->format('d-m-Y')); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p class="text-gray-500">Belum ada feedback terbaru.</p>
    <?php endif; ?>
</div>



<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
const ctx = document.getElementById('laporanChart').getContext('2d');
const laporanChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode($labels, 15, 512) ?>,
        datasets: [{
            label: 'Total Laporan',
            data: <?php echo json_encode($data, 15, 512) ?>,
            backgroundColor: 'rgba(59, 130, 246, 0.5)',
            borderColor: 'rgba(59, 130, 246, 1)',
            borderWidth: 1
        }]
    },
    options: {
        scales: {
            y: {
                beginAtZero: true,
                ticks: { precision: 0 }
            }
        }
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('dashboard.template', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\web\LaporinAja\resources\views/dashboard/admin.blade.php ENDPATH**/ ?>