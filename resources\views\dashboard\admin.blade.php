@extends('dashboard.template')

@section('title', $title)

@section('content')

{{-- Header --}}
<div class="flex justify-between items-center mb-6">
    <div>
        <h1 class="text-2xl font-bold text-gray-800">Dashboard Admin</h1>
        <p class="text-sm text-gray-600">Terakhir diperbarui: {{ now()->format('d/m/Y H:i:s') }}</p>
    </div>
    <button onclick="window.location.reload()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow flex items-center space-x-2">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        <span>Refresh Data</span>
    </button>
</div>

{{-- Kartu Statistik --}}
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
    @foreach ($cards as $card)
        <a href="{{ $card['url'] }}" class="flex items-center space-x-4 p-5 bg-{{ $card['color'] }}-100 rounded-xl shadow hover:shadow-lg hover:scale-[1.02] transition transform">
            <div class="p-3 bg-{{ $card['color'] }}-200 rounded-full">
                @switch($card['title'])
                    @case('Total Laporan')
                        <svg class="w-6 h-6 text-{{ $card['color'] }}-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M9 17v-6h13V7H9V3L2 12l7 9z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        @break
                    @case('Belum Diproses')
                        <svg class="w-6 h-6 text-{{ $card['color'] }}-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M12 8v4l3 3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="12" r="10" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        @break
                    @case('Diproses')
                        <svg class="w-6 h-6 text-{{ $card['color'] }}-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M4 4h16v16H4z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        @break
                    @case('Selesai')
                        <svg class="w-6 h-6 text-{{ $card['color'] }}-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M5 13l4 4L19 7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        @break
                    @case('Total Pengguna')
                        <svg class="w-6 h-6 text-{{ $card['color'] }}-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M17 20h5V4H2v16h5m10 0v-6H7v6h10zM7 12h10M7 16h10" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        @break
                    @case('Pembuatan Akun Petugas')
                        <svg class="w-6 h-6 text-{{ $card['color'] }}-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M12 4v16m8-8H4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        @break
                @endswitch
            </div>
            <div>
                <h2 class="text-lg font-bold text-{{ $card['color'] }}-800">{{ $card['title'] }}</h2>
                <p class="text-gray-700 mt-1 font-medium">{{ $card['desc'] }}</p>
            </div>
        </a>
    @endforeach
</div>

{{-- Statistik per Kategori --}}
<h2 class="text-xl font-bold mb-4">Grafik Laporan per Bulan</h2>
<canvas id="laporanChart" height="100"></canvas>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    const ctx = document.getElementById('laporanChart').getContext('2d');
    const laporanChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: @json($labels),
            datasets: [{
                label: 'Total Laporan',
                data: @json($data),
                backgroundColor: 'rgba(54, 162, 235, 0.7)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true,
                    precision: 0
                }
            }
        }
    });
</script>

{{-- Tabel Laporan Terbaru --}}
<div class="bg-white p-6 rounded-lg shadow">
    <h3 class="text-lg font-bold mb-4">Laporan Terbaru</h3>
    @if($laporanTerbaru->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full text-left text-sm">
                <thead>
                    <tr class="bg-gray-100 text-gray-700">
                        <th class="py-2 px-4">Judul</th>
                        <th class="py-2 px-4">Kategori</th>
                        <th class="py-2 px-4">Status</th>
                        <th class="py-2 px-4">Tanggal & Waktu</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($laporanTerbaru as $laporan)
                        <tr class="border-b">
                            <td class="py-2 px-4">{{ $laporan->judul }}</td>
                            <td class="py-2 px-4">{{ $laporan->kategori ?? '-' }}</td>
                            <td class="py-2 px-4">
                                <span class="px-2 py-1 rounded-full text-xs {{
                                    $laporan->status == 'selesai' ? 'bg-green-200 text-green-800' :
                                    ($laporan->status == 'diproses' ? 'bg-yellow-200 text-yellow-800' : 'bg-gray-200 text-gray-800')
                                }}">
                                    {{ ucfirst($laporan->status) }}
                                </span>
                            </td>
                            <td class="py-2 px-4">
                                <div class="flex flex-col">
                                    <span class="font-medium">{{ $laporan->created_at->format('d/m/Y') }}</span>
                                    <span class="text-xs text-gray-500">{{ $laporan->created_at->format('H:i') }} WIB</span>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @else
        <p class="text-gray-500">Belum ada laporan terbaru.</p>
    @endif
</div>

{{-- Tabel Feedback Terbaru --}}
<div class="bg-white p-6 rounded-lg shadow mt-6">
    <h3 class="text-lg font-bold mb-4">Feedback Terbaru</h3>
    @if($feedbackTerbaru->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full text-left text-sm">
                <thead>
                    <tr class="bg-gray-100 text-gray-700">
                        <th class="py-2 px-4">Nama</th>
                        <th class="py-2 px-4">Pesan</th>
                        <th class="py-2 px-4">Rating</th>
                        <th class="py-2 px-4">Tanggal</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($feedbackTerbaru as $feedback)
                        <tr class="border-b">
                            <td class="py-2 px-4">{{ $feedback->user->nama ?? $feedback->nama ?? '-' }}</td>
                            <td class="py-2 px-4">{{ $feedback->pesan }}</td>
                            <td class="py-2 px-4">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= $feedback->rating)
                                        <span class="text-yellow-400">★</span>
                                    @else
                                        <span class="text-gray-300">★</span>
                                    @endif
                                @endfor
                            </td>
                            <td class="py-2 px-4">{{ $feedback->created_at->format('d-m-Y') }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @else
        <p class="text-gray-500">Belum ada feedback terbaru.</p>
    @endif
</div>


{{-- Chart.js --}}
@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
const ctx = document.getElementById('laporanChart').getContext('2d');
const laporanChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: @json($labels),
        datasets: [{
            label: 'Total Laporan',
            data: @json($data),
            backgroundColor: 'rgba(59, 130, 246, 0.5)',
            borderColor: 'rgba(59, 130, 246, 1)',
            borderWidth: 1
        }]
    },
    options: {
        scales: {
            y: {
                beginAtZero: true,
                ticks: { precision: 0 }
            }
        }
    }
});
</script>
@endpush

@endsection
