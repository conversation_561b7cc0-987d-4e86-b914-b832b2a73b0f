@extends('dashboard.template')

@section('title', 'Daftar Laporan')

@section('content')
<div class="max-w-7xl mx-auto">
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100">Daftar Laporan</h1>

        <!-- Back to Dashboard Button -->
        @if(auth()->user()->role === 'petugas')
            <a href="{{ route('dashboard.petugas') }}"
               class="bg-blue-600 hover:bg-blue-700 text-white px-3 sm:px-4 py-2 rounded-lg shadow transition duration-200 flex items-center justify-center text-sm sm:text-base">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <span class="hidden sm:inline">Kembali ke Dashboard</span>
                <span class="sm:hidden">Dashboard</span>
            </a>
        @elseif(auth()->user()->role === 'admin')
            <a href="{{ route('admin.dashboard') }}"
               class="bg-blue-600 hover:bg-blue-700 text-white px-3 sm:px-4 py-2 rounded-lg shadow transition duration-200 flex items-center justify-center text-sm sm:text-base">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <span class="hidden sm:inline">Kembali ke Dashboard</span>
                <span class="sm:hidden">Dashboard</span>
            </a>
        @elseif(auth()->user()->role === 'user')
            <a href="{{ route('user.dashboard') }}"
               class="bg-blue-600 hover:bg-blue-700 text-white px-3 sm:px-4 py-2 rounded-lg shadow transition duration-200 flex items-center justify-center text-sm sm:text-base">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <span class="hidden sm:inline">Kembali ke Dashboard</span>
                <span class="sm:hidden">Dashboard</span>
            </a>
        @endif
    </div>

    <!-- Filter Status dan Prioritas -->
    <div class="mb-6 bg-white dark:bg-white p-4 sm:p-6 rounded-xl shadow-lg">
        <!-- Filter Status -->
        <div class="mb-4">
            <h3 class="text-sm sm:text-base font-medium text-black dark:text-black mb-3">Filter Status:</h3>
            <div class="flex flex-wrap gap-2">
                @if(auth()->user()->role === 'user')`
                    <a href="{{ route('user.laporan.index') }}"
                       class="px-3 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors {{ !request('status') ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500' }}">
                        📋 Semua
                    </a>
                    <a href="{{ route('user.laporan.index', ['status' => 'belum_diproses']) }}"
                       class="px-3 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors {{ request('status') == 'belum_diproses' ? 'bg-red-600 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500' }}">
                        ⏳ Belum Diproses
                    </a>
            <a href="{{ route('user.laporan.index', ['status' => 'diproses']) }}"
               class="px-3 py-1 rounded text-sm {{ request('status') == 'diproses' ? 'bg-yellow-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                Diproses
            </a>
            <a href="{{ route('user.laporan.index', ['status' => 'selesai']) }}"
               class="px-3 py-1 rounded text-sm {{ request('status') == 'selesai' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                Selesai
            </a>
        @else
            <a href="{{ route('laporan.index') }}"
               class="px-3 py-1 rounded text-sm {{ !request('status') ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                Semua
            </a>
            <a href="{{ route('laporan.index', ['status' => 'belum_diproses']) }}"
               class="px-3 py-1 rounded text-sm {{ request('status') == 'belum_diproses' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                Belum Diproses
            </a>
            <a href="{{ route('laporan.index', ['status' => 'diproses']) }}"
               class="px-3 py-1 rounded text-sm {{ request('status') == 'diproses' ? 'bg-yellow-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                Diproses
            </a>
            <a href="{{ route('laporan.index', ['status' => 'selesai']) }}"
               class="px-3 py-1 rounded text-sm {{ request('status') == 'selesai' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                Selesai
            </a>
        @endif
    </div>

    @if(in_array(auth()->user()->role, ['admin', 'petugas']))
    <!-- Filter Prioritas -->
    <div class="flex flex-wrap gap-2">
        <span class="text-sm font-medium text-gray-700 self-center">Prioritas:</span>
        <a href="{{ route('laporan.index', array_filter(['status' => request('status')])) }}"
           class="px-3 py-1 rounded text-sm {{ !request('prioritas') ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
            Semua
        </a>
        <a href="{{ route('laporan.index', array_filter(['status' => request('status'), 'prioritas' => 'tinggi'])) }}"
           class="px-3 py-1 rounded text-sm {{ request('prioritas') == 'tinggi' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
            🔴 Tinggi
        </a>
        <a href="{{ route('laporan.index', array_filter(['status' => request('status'), 'prioritas' => 'sedang'])) }}"
           class="px-3 py-1 rounded text-sm {{ request('prioritas') == 'sedang' ? 'bg-yellow-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
            🟡 Sedang
        </a>
        <a href="{{ route('laporan.index', array_filter(['status' => request('status'), 'prioritas' => 'rendah'])) }}"
           class="px-3 py-1 rounded text-sm {{ request('prioritas') == 'rendah' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
            🟢 Rendah
        </a>
    </div>
    @endif
</div>

    <!-- Desktop Table View (hidden on mobile) -->
    <div class="hidden lg:block bg-white dark:bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="bg-blue-600 dark:bg-blue-700 text-white">
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">#</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Judul</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Kategori</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Lokasi</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Prioritas</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Tanggal Dibuat</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Pelapor</th>
                        <th class="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider">Aksi</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-white divide-y divide-gray-200 dark:divide-gray-600">
            @forelse ($laporans as $key => $laporan)
            <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ $key + 1 + ($laporans->currentPage() - 1) * $laporans->perPage() }}
                </td>
                <td class="px-4 py-4">
                    <div class="text-sm font-medium text-gray-900">{{ $laporan->judul }}</div>
                    <div class="text-sm text-gray-500">{{ Str::limit($laporan->isi, 50) }}</div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        {{ $laporan->kategori ?? 'Tidak ada kategori' }}
                    </span>
                </td>
                <td class="px-4 py-4">
                    <div class="text-sm text-gray-900">
                        @if($laporan->alamat)
                            <div class="font-medium">{{ Str::limit($laporan->alamat, 30) }}</div>
                            <div class="text-gray-500">RT {{ $laporan->rt ?? '-' }} / RW {{ $laporan->rw ?? '-' }}</div>
                        @else
                            <span class="text-gray-400 italic">Alamat tidak tersedia</span>
                        @endif
                    </div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    @if(in_array(auth()->user()->role, ['admin', 'petugas']))
                        <!-- Form untuk mengubah prioritas -->
                        <form action="{{ route('laporan.prioritas', $laporan->id) }}" method="POST" class="inline-block">
                            @csrf
                            <select name="prioritas" onchange="this.form.submit()"
                                    class="text-xs font-semibold rounded-full border-0 focus:ring-2 focus:ring-blue-500
                                    @if($laporan->prioritas == 'tinggi') bg-red-100 text-red-800
                                    @elseif($laporan->prioritas == 'sedang') bg-yellow-100 text-yellow-800
                                    @else bg-green-100 text-green-800 @endif">
                                <option value="rendah" {{ $laporan->prioritas == 'rendah' ? 'selected' : '' }}>🟢 Rendah</option>
                                <option value="sedang" {{ $laporan->prioritas == 'sedang' ? 'selected' : '' }}>🟡 Sedang</option>
                                <option value="tinggi" {{ $laporan->prioritas == 'tinggi' ? 'selected' : '' }}>🔴 Tinggi</option>
                            </select>
                        </form>
                    @else
                        <!-- Tampilan read-only untuk user -->
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            @if($laporan->prioritas == 'tinggi') bg-red-100 text-red-800
                            @elseif($laporan->prioritas == 'sedang') bg-yellow-100 text-yellow-800
                            @else bg-green-100 text-green-800 @endif">
                            @if($laporan->prioritas == 'tinggi') 🔴 Tinggi
                            @elseif($laporan->prioritas == 'sedang') 🟡 Sedang
                            @else 🟢 Rendah @endif
                        </span>
                    @endif
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    @if ($laporan->status == 'belum_diproses')
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Belum Diproses</span>
                    @elseif ($laporan->status == 'diproses')
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Diproses</span>
                    @else
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Selesai</span>
                    @endif
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div class="flex flex-col">
                        <div class="font-medium">{{ $laporan->created_at->format('d/m/Y') }}</div>
                        <div class="text-xs text-gray-500">{{ $laporan->created_at->format('H:i') }} WIB</div>
                    </div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-8 w-8">
                            <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                                <span class="text-white font-medium text-xs">
                                    {{ strtoupper(substr($laporan->user->name, 0, 1)) }}
                                </span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-900">{{ $laporan->user->name }}</div>
                        </div>
                    </div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-center text-sm font-medium">
                    <div class="flex items-center justify-center space-x-2">
                        <a href="{{ route('laporan.show', $laporan->id) }}"
                           class="inline-flex items-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition duration-200">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Lihat
                        </a>

                        @if ($laporan->status == 'belum_diproses')
                            <a href="{{ route('laporan.proses', $laporan->id) }}"
                               class="inline-flex items-center px-2 py-1 bg-yellow-500 hover:bg-yellow-600 text-white text-xs font-medium rounded-md transition duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Proses
                            </a>
                        @elseif ($laporan->status == 'diproses')
                            <a href="{{ route('laporan.selesai', $laporan->id) }}"
                               class="inline-flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Selesai
                            </a>
                        @endif
                    </div>
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="9" class="px-4 py-8 text-center">
                    <div class="flex flex-col items-center">
                        <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="text-gray-500 text-lg font-medium">Belum ada laporan.</p>
                    </div>
                </td>
            </tr>
            @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Mobile Card View (visible on mobile) -->
    <div class="lg:hidden space-y-4">
        @forelse ($laporans as $key => $laporan)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-200 dark:border-gray-600">
                <!-- Header with number and status -->
                <div class="flex justify-between items-start mb-3">
                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                        #{{ $key + 1 + ($laporans->currentPage() - 1) * $laporans->perPage() }}
                    </span>
                    <div class="flex flex-col items-end gap-1">
                        @if ($laporan->status == 'belum_diproses')
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">⏳ Belum Diproses</span>
                        @elseif ($laporan->status == 'diproses')
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">🔄 Diproses</span>
                        @else
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">✅ Selesai</span>
                        @endif

                        <!-- Priority -->
                        @if(in_array(auth()->user()->role, ['admin', 'petugas']))
                            <form action="{{ route('laporan.prioritas', $laporan->id) }}" method="POST" class="inline-block">
                                @csrf
                                <select name="prioritas" onchange="this.form.submit()"
                                        class="text-xs font-semibold rounded-full border-0 focus:ring-2 focus:ring-blue-500 px-2 py-1
                                        @if($laporan->prioritas == 'tinggi') bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300
                                        @elseif($laporan->prioritas == 'sedang') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300
                                        @else bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 @endif">
                                    <option value="rendah" {{ $laporan->prioritas == 'rendah' ? 'selected' : '' }}>🟢 Rendah</option>
                                    <option value="sedang" {{ $laporan->prioritas == 'sedang' ? 'selected' : '' }}>🟡 Sedang</option>
                                    <option value="tinggi" {{ $laporan->prioritas == 'tinggi' ? 'selected' : '' }}>🔴 Tinggi</option>
                                </select>
                            </form>
                        @else
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                @if($laporan->prioritas == 'tinggi') bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300
                                @elseif($laporan->prioritas == 'sedang') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300
                                @else bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 @endif">
                                @if($laporan->prioritas == 'tinggi') 🔴 Tinggi
                                @elseif($laporan->prioritas == 'sedang') 🟡 Sedang
                                @else 🟢 Rendah @endif
                            </span>
                        @endif
                    </div>
                </div>

                <!-- Title and description -->
                <div class="mb-3">
                    <h3 class="font-semibold text-gray-900 dark:text-gray-100 text-sm mb-1">{{ $laporan->judul }}</h3>
                    <p class="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">{{ Str::limit($laporan->isi, 80) }}</p>
                </div>

                <!-- Category and Location -->
                <div class="grid grid-cols-1 gap-2 mb-3">
                    <div class="flex items-center">
                        <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">📂</span>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                            {{ $laporan->kategori ?? 'Tidak ada kategori' }}
                        </span>
                    </div>
                    <div class="flex items-start">
                        <span class="text-xs text-gray-500 dark:text-gray-400 mr-2 mt-0.5">📍</span>
                        <div class="text-xs text-gray-600 dark:text-gray-400">
                            @if($laporan->alamat)
                                <div class="font-medium">{{ Str::limit($laporan->alamat, 40) }}</div>
                                <div class="text-gray-500 dark:text-gray-500">RT {{ $laporan->rt ?? '-' }} / RW {{ $laporan->rw ?? '-' }}</div>
                            @else
                                <span class="text-gray-400 italic">Alamat tidak tersedia</span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Reporter and Date -->
                <div class="grid grid-cols-1 gap-2 mb-3">
                    <div class="flex items-center">
                        <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">👤</span>
                        <span class="text-xs text-gray-600 dark:text-gray-400">{{ $laporan->user->name ?? 'Tidak diketahui' }}</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">📅</span>
                        <div class="text-xs text-gray-600 dark:text-gray-400">
                            <span class="font-medium">{{ $laporan->created_at->format('d/m/Y') }}</span>
                            <span class="text-gray-500 dark:text-gray-500">{{ $laporan->created_at->format('H:i') }} WIB</span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex flex-wrap gap-2 pt-3 border-t border-gray-200 dark:border-gray-600">
                    <a href="{{ route('laporan.show', $laporan->id) }}"
                       class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition duration-200">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        Lihat Detail
                    </a>

                    @if(in_array(auth()->user()->role, ['admin', 'petugas']))
                        @if ($laporan->status == 'belum_diproses')
                            <a href="{{ route('laporan.proses', $laporan->id) }}"
                               class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-yellow-500 hover:bg-yellow-600 text-white text-xs font-medium rounded-md transition duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Proses
                            </a>
                        @elseif ($laporan->status == 'diproses')
                            <a href="{{ route('laporan.selesai', $laporan->id) }}"
                               class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Selesai
                            </a>
                        @endif
                    @endif
                </div>
            </div>
        @empty
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
                <div class="flex flex-col items-center">
                    <svg class="w-16 h-16 text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-gray-500 dark:text-gray-400 text-lg font-medium mb-2">Belum ada laporan</p>
                    <p class="text-gray-400 dark:text-gray-500 text-sm">Laporan akan muncul di sini setelah dibuat</p>
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        {{ $laporans->links() }}
    </div>
</div>
@endsection
