<?php $__env->startSection('title', 'Detail Laporan'); ?>

<?php $__env->startSection('content'); ?>
    <h1 class="text-2xl font-bold mb-6">Detail Laporan</h1>

    <div class="bg-white shadow rounded p-6 space-y-4">

        
        <div>
            <h2 class="font-semibold text-lg mb-1">Judul</h2>
            <p class="text-gray-700"><?php echo e($laporan->judul); ?></p>
        </div>

        
        <div>
            <h2 class="font-semibold text-lg mb-1">Kategori</h2>
            <p class="text-gray-700"><?php echo e($laporan->kategori); ?></p>
        </div>

        
        <div>
            <h2 class="font-semibold text-lg mb-3">📍 Lokasi</h2>
            <div class="text-gray-700 space-y-3">
                <p><strong>Alamat:</strong> <?php echo e($laporan->alamat ?? 'Tidak ada alamat'); ?></p>
                <p><strong>RT/RW:</strong> <?php echo e($laporan->rt ?? '-'); ?> / <?php echo e($laporan->rw ?? '-'); ?></p>

                <?php if($laporan->latitude && $laporan->longitude): ?>
                    <div class="bg-gray-50 p-4 rounded-lg border">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-medium text-gray-800">🗺️ Peta Lokasi</h3>
                            <a href="https://www.google.com/maps/dir/?api=1&destination=<?php echo e($laporan->latitude); ?>,<?php echo e($laporan->longitude); ?>"
                                target="_blank"
                                class="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Navigasi ke Lokasi
                            </a>
                        </div>

                        <div id="detail-map" class="w-full h-64 border rounded-lg bg-gray-100 flex items-center justify-center">
                            <div class="text-center text-gray-500">
                                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <p class="text-sm">Memuat peta...</p>
                            </div>
                        </div>

                        <div class="mt-3 grid grid-cols-2 gap-4 text-sm">
                            <div class="bg-white p-2 rounded border">
                                <span class="text-gray-600">Latitude:</span>
                                <span class="font-mono"><?php echo e(number_format($laporan->latitude, 6)); ?></span>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <span class="text-gray-600">Longitude:</span>
                                <span class="font-mono"><?php echo e(number_format($laporan->longitude, 6)); ?></span>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <p class="text-yellow-800 text-sm flex items-center">
                            Koordinat lokasi tidak tersedia untuk laporan ini.
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        
        <div>
            <h2 class="font-semibold text-lg mb-1">Isi Laporan</h2>
            <p class="text-gray-700"><?php echo e($laporan->isi); ?></p>
        </div>

        
        <div>
            <h2 class="font-semibold text-lg mb-1">Status</h2>
            <span
                class="inline-block px-3 py-1 rounded text-white
                <?php if($laporan->status == 'belum_diproses'): ?> bg-gray-500
                <?php elseif($laporan->status == 'diproses'): ?> bg-yellow-500
                <?php else: ?> bg-green-600 <?php endif; ?>">
                <?php echo e(ucfirst($laporan->status)); ?>

            </span>
        </div>

        
        <div>
            <h2 class="font-semibold text-lg mb-1">Prioritas</h2>
            <div class="flex items-center space-x-3">
                <span
                    class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
                    <?php if($laporan->prioritas == 'tinggi'): ?> bg-red-100 text-red-800
                    <?php elseif($laporan->prioritas == 'sedang'): ?> bg-yellow-100 text-yellow-800
                    <?php else: ?> bg-green-100 text-green-800 <?php endif; ?>">
                    <?php if($laporan->prioritas == 'tinggi'): ?>
                        🔴 Prioritas Tinggi
                    <?php elseif($laporan->prioritas == 'sedang'): ?>
                        🟡 Prioritas Sedang
                    <?php else: ?>
                        🟢 Prioritas Rendah
                    <?php endif; ?>
                </span>

                <?php if(in_array(auth()->user()->role, ['admin', 'petugas'])): ?>
                    <form action="<?php echo e(route('laporan.prioritas', $laporan->id)); ?>" method="POST" class="inline-block">
                        <?php echo csrf_field(); ?>
                        <select name="prioritas" onchange="this.form.submit()"
                            class="text-xs border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500">
                            <option value="rendah" <?php echo e($laporan->prioritas == 'rendah' ? 'selected' : ''); ?>>Rendah</option>
                            <option value="sedang" <?php echo e($laporan->prioritas == 'sedang' ? 'selected' : ''); ?>>Sedang</option>
                            <option value="tinggi" <?php echo e($laporan->prioritas == 'tinggi' ? 'selected' : ''); ?>>Tinggi</option>
                        </select>
                    </form>
                <?php endif; ?>
            </div>
        </div>

        
        <div>
            <h2 class="font-semibold text-lg mb-1">Pelapor</h2>
            <p class="text-gray-700"><?php echo e($laporan->user->name ?? '-'); ?></p>
        </div>

        
        <?php if($laporan->bukti_awal): ?>
            <div>
                <h2 class="font-semibold text-lg mb-3">Bukti Kondisi Awal</h2>
                <img src="<?php echo e(asset('storage/' . $laporan->bukti_awal)); ?>"
                    class="w-full max-w-md rounded-lg shadow-md border" alt="Bukti Awal">
            </div>
        <?php endif; ?>

        
        <?php if($laporan->bukti): ?>
    <div>
        <h2 class="font-semibold text-lg mb-3">
            <?php echo e(auth()->user()->role == 'user' ? 'Bukti Penyelesaian' : 'Bukti Laporan dari User'); ?>

        </h2>
        <img src="<?php echo e(asset('storage/' . $laporan->bukti)); ?>" class="w-full max-w-md rounded-lg shadow-md border" alt="Bukti">

        <?php if(in_array(auth()->user()->role, ['admin', 'petugas'])): ?>
            <a href="<?php echo e(asset('storage/' . $laporan->bukti)); ?>" download
                                class="inline-flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Download Bukti
                            </a>
        <?php endif; ?>
    </div>
<?php endif; ?>

        
        <div class="mt-6">
            <?php
                $user = auth()->user();
                $backRoute = '#';
                $backText = '← Kembali';

                if ($user->role === 'admin' || $user->role === 'petugas') {
                    $backRoute = route('laporan.index');
                    $backText = '← Kembali ke Laporan';
                } elseif ($user->role === 'user') {
                    $backRoute = route('user.dashboard');
                    $backText = '← Kembali ke Dashboard';
                }
            ?>

            <a href="<?php echo e($backRoute); ?>" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                <?php echo e($backText); ?>

            </a>
        </div>


        
        <?php if(auth()->user()->role == 'user' && $laporan->status == 'selesai' && !$laporan->rating): ?>
            <div class="mt-6 bg-white shadow rounded p-6">
                <h2 class="font-semibold text-lg mb-4">Beri Rating Laporan Ini</h2>
                <form action="<?php echo e(route('rating.store')); ?>" method="POST" class="space-y-4">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="laporan_id" value="<?php echo e($laporan->id); ?>">

                    <div>
                        <label for="rating" class="block font-medium mb-1">Rating (1 - 5 Bintang)</label>
                        <select name="rating" required class="w-full border rounded px-3 py-2">
                            <option value="">Pilih Bintang</option>
                            <option value="1">1 - Sangat Buruk</option>
                            <option value="2">2 - Buruk</option>
                            <option value="3">3 - Cukup</option>
                            <option value="4">4 - Baik</option>
                            <option value="5">5 - Sangat Baik</option>
                        </select>
                    </div>

                    <div>
                        <label for="komentar" class="block font-medium mb-1">Komentar (Opsional)</label>
                        <textarea name="komentar" rows="3" class="w-full border rounded px-3 py-2" placeholder="Tulis komentar Anda..."></textarea>
                    </div>

                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Kirim
                        Rating</button>
                </form>
            </div>
        <?php endif; ?>

    </div>
<?php $__env->stopSection(); ?>

<?php if($laporan->latitude && $laporan->longitude): ?>
<?php $__env->startSection('scripts'); ?>
    <!-- Check if Google Maps API key is configured -->
    <?php if(env('GOOGLE_MAPS_API_KEY') && env('GOOGLE_MAPS_API_KEY') !== 'YOUR_GOOGLE_MAPS_API_KEY_HERE'): ?>
        <!-- Google Maps JavaScript API -->
        <script async defer
            src="https://maps.googleapis.com/maps/api/js?key=<?php echo e(env('GOOGLE_MAPS_API_KEY')); ?>&callback=initDetailMap"
            onerror="handleDetailMapError()"></script>
    <?php else: ?>
        <!-- No valid API key, show fallback immediately -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                showDetailMapError();
            });
        </script>
    <?php endif; ?>

    <script>
        // Error handling for Google Maps API
        function handleDetailMapError() {
            console.error('Google Maps API failed to load');
            showDetailMapError();
        }

        function showDetailMapError() {
            const mapContainer = document.getElementById('detail-map');
            if (mapContainer) {
                mapContainer.innerHTML = `
                    <div class="flex items-center justify-center h-full bg-red-50 border-2 border-red-200 rounded-lg">
                        <div class="text-center p-6">
                            <svg class="w-12 h-12 text-red-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-red-800 mb-2">Peta Tidak Tersedia</h3>
                            <p class="text-sm text-red-600 mb-3">Google Maps tidak dapat dimuat.</p>
                            <a href="https://www.google.com/maps/dir/?api=1&destination=<?php echo e($laporan->latitude); ?>,<?php echo e($laporan->longitude); ?>"
                               target="_blank"
                               class="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                Buka di Google Maps
                            </a>
                        </div>
                    </div>
                `;
            }
        }

        function initDetailMap() {
            try {
                // Check if Google Maps is available
                if (typeof google === 'undefined' || !google.maps) {
                    throw new Error('Google Maps API not loaded');
                }

                const location = {
                    lat: <?php echo e($laporan->latitude); ?>,
                    lng: <?php echo e($laporan->longitude); ?>

                };

                const map = new google.maps.Map(document.getElementById('detail-map'), {
                    zoom: 15,
                    center: location,
                    mapTypeControl: true,
                    streetViewControl: true,
                    fullscreenControl: true,
                    zoomControl: true,
                    styles: [
                        {
                            featureType: 'poi',
                            elementType: 'labels',
                            stylers: [{ visibility: 'on' }]
                        }
                    ]
                });

                const marker = new google.maps.Marker({
                    position: location,
                    map: map,
                    animation: google.maps.Animation.DROP,
                    title: '<?php echo e($laporan->judul); ?>',
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="#dc2626">
                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(32, 32),
                        anchor: new google.maps.Point(16, 32)
                    }
                });

                // Add info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div class="p-2">
                            <h3 class="font-semibold text-sm mb-1"><?php echo e($laporan->judul); ?></h3>
                            <p class="text-xs text-gray-600 mb-1"><?php echo e($laporan->kategori); ?></p>
                            <p class="text-xs text-gray-500"><?php echo e($laporan->alamat ?? 'Alamat tidak tersedia'); ?></p>
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    infoWindow.open(map, marker);
                });

                // Auto-open info window
                setTimeout(() => {
                    infoWindow.open(map, marker);
                }, 1000);

            } catch (error) {
                console.error('Error initializing detail map:', error);
                showDetailMapError();
            }
        }
    </script>
<?php $__env->stopSection(); ?>
<?php endif; ?>

<?php echo $__env->make('dashboard.template', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\web\LaporinAja\resources\views/laporan/show.blade.php ENDPATH**/ ?>