<?php $__env->startSection('title', 'Daftar Pengguna'); ?>

<?php $__env->startSection('content'); ?>

<?php if(session('success')): ?>
    <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <?php echo e(session('success')); ?>

    </div>
<?php endif; ?>

<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">
        <?php if(isset($role)): ?>
            <?php if($role === 'petugas' && auth()->user()->role === 'petugas'): ?>
                Profil <PERSON>
            <?php else: ?>
                Daftar <?php echo e(ucfirst($role)); ?>

            <?php endif; ?>
        <?php else: ?>
            Daftar Pengguna
        <?php endif; ?>
    </h1>

    <div class="flex items-center space-x-3">
        <?php if(auth()->user()->role === 'admin' && $role === 'petugas'): ?>
            <a href="<?php echo e(route('pengguna.create-petugas')); ?>"
               class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg shadow flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Buat Akun Petugas
            </a>
        <?php endif; ?>

        <?php if(auth()->user()->role === 'petugas'): ?>
            <a href="<?php echo e(route('dashboard.petugas')); ?>"
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Kembali ke Dashboard
            </a>
        <?php elseif(auth()->user()->role === 'admin'): ?>
            <a href="<?php echo e(route('admin.dashboard')); ?>"
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Kembali ke Dashboard
            </a>
        <?php endif; ?>
    </div>
</div>

<div class="bg-white rounded-lg shadow overflow-hidden">
    <table class="min-w-full text-sm text-left text-gray-700">
        <thead class="bg-blue-600 text-white">
            <tr>
                <th class="px-4 py-3">#</th>
                <th class="px-4 py-3">Nama</th>
                <th class="px-4 py-3">Email</th>
                <th class="px-4 py-3">Role</th>
                <th class="px-4 py-3">Tanggal Daftar</th>
                <?php if(auth()->user()->role === 'admin'): ?>
                    <th class="px-4 py-3 text-center">Aksi</th>
                <?php endif; ?>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr class="hover:bg-gray-50">
                    <td class="px-4 py-2">
                        <?php echo e($loop->iteration + ($users->currentPage() - 1) * $users->perPage()); ?>

                    </td>
                    <td class="px-4 py-2 flex items-center space-x-2">
                        <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-semibold">
                            <?php echo e(strtoupper(substr($user->name, 0, 1))); ?>

                        </div>
                        <span><?php echo e($user->name); ?></span>
                    </td>
                    <td class="px-4 py-2"><?php echo e($user->email); ?></td>
                    <td class="px-4 py-2">
                        <span class="inline-block px-2 py-1 text-xs font-semibold rounded
                            <?php echo e($user->role == 'admin' ? 'bg-red-100 text-red-700' :
                               ($user->role == 'petugas' ? 'bg-yellow-100 text-yellow-700' :
                               'bg-green-100 text-green-700')); ?>">
                            <?php echo e(ucfirst($user->role)); ?>

                        </span>
                    </td>
                    <td class="px-4 py-2 text-gray-500"><?php echo e($user->created_at->format('d M Y')); ?></td>
                    <?php if(auth()->user()->role === 'admin'): ?>
                        <td class="px-4 py-2 text-center space-x-2">
                            <a href="<?php echo e(route('pengguna.edit', $user)); ?>"
                               class="inline-flex items-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-md">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Edit
                            </a>

                            <?php if($user->id !== auth()->id()): ?>
                                <form action="<?php echo e(route('pengguna.destroy', $user)); ?>" method="POST" class="inline-block" onsubmit="return confirm('Yakin hapus <?php echo e($user->name); ?>?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit"
                                            class="inline-flex items-center px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded-md">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M9 4V3a1 1 0 011-1h4a1 1 0 011 1v1"></path>
                                        </svg>
                                        Hapus
                                    </button>
                                </form>
                            <?php endif; ?>
                        </td>
                    <?php endif; ?>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="<?php echo e(auth()->user()->role === 'admin' ? '6' : '5'); ?>" class="px-4 py-8 text-center text-gray-500">
                        <?php if(isset($role)): ?>
                            Belum ada <?php echo e($role); ?> terdaftar.
                        <?php else: ?>
                            Belum ada pengguna terdaftar.
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<div class="mt-4">
    <?php echo e($users->links()); ?>

</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('dashboard.template', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\web\LaporinAja\resources\views/pengguna/index.blade.php ENDPATH**/ ?>