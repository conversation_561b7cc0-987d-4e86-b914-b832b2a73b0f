<?php $__env->startSection('title', 'Buat Laporan'); ?>

<?php $__env->startSection('content'); ?>
    <div class="max-w-4xl mx-auto">
        <h1 class="text-xl sm:text-2xl font-bold mb-4 sm:mb-6">Buat Laporan</h1>

        <?php if($errors->any()): ?>
            <div class="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 p-3 sm:p-4 rounded-lg mb-4 sm:mb-6">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="text-sm sm:text-base"><?php echo e($error); ?></div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>

        <form action="<?php echo e(route('lapor.store')); ?>" method="POST" enctype="multipart/form-data"
            class="bg-white dark:bg-gray-800 p-4 sm:p-6 lg:p-8 rounded-xl shadow-lg space-y-4 sm:space-y-6">
            <?php echo csrf_field(); ?>

            <div>
                <label for="judul"
                    class="block font-semibold text-gray-700 dark:text-gray-300 mb-2 text-sm sm:text-base">Judul
                    Laporan</label>
                <input type="text" name="judul" id="judul"
                    class="w-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors duration-300 text-sm sm:text-base"
                    placeholder="Masukkan judul laporan yang jelas dan singkat" required>
            </div>

            <div>
                <label for="kategori"
                    class="block font-semibold text-gray-700 dark:text-gray-300 mb-2 text-sm sm:text-base">Kategori</label>
                <select name="kategori" id="kategori"
                    class="w-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors duration-300 text-sm sm:text-base"
                    required>
                    <option value="">-- Pilih Kategori --</option>
                    <option value="Infrastruktur">🏗️ Infrastruktur</option>
                    <option value="Lingkungan">🌱 Lingkungan</option>
                    <option value="Keamanan">🔒 Keamanan</option>
                    <option value="Lainnya">📋 Lainnya</option>
                </select>
            </div>

            <!-- Alamat -->
            <div>
                <label for="alamat"
                    class="block font-semibold text-gray-700 dark:text-gray-300 mb-2 text-sm sm:text-base">Alamat
                    Lengkap</label>
                <textarea name="alamat" id="alamat" rows="3"
                    class="w-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors duration-300 text-sm sm:text-base"
                    placeholder="Contoh: Jl. Merdeka No. 123, Kelurahan ABC, Kecamatan XYZ" required></textarea>
            </div>

            <!-- RT dan RW -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                    <label for="rt"
                        class="block font-semibold text-gray-700 dark:text-gray-300 mb-2 text-sm sm:text-base">RT (Rukun
                        Tetangga)</label>
                    <input type="text" name="rt" id="rt"
                        class="w-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors duration-300 text-sm sm:text-base"
                        placeholder="Contoh: 001" maxlength="3" required>
                </div>
                <div>
                    <label for="rw"
                        class="block font-semibold text-gray-700 dark:text-gray-300 mb-2 text-sm sm:text-base">RW (Rukun
                        Warga)</label>
                    <input type="text" name="rw" id="rw"
                        class="w-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors duration-300 text-sm sm:text-base"
                        placeholder="Contoh: 005" maxlength="3" required>
                </div>
            </div>

            <!-- Google Maps Location Picker -->
            <!-- Google Maps Location Picker -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 sm:p-6 rounded-lg">
                <label class="block font-semibold text-gray-700 dark:text-gray-300 mb-2 text-sm sm:text-base">📍 Pilih
                    Lokasi di Peta</label>
                <p class="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mb-3">Klik pada peta untuk menandai lokasi
                    kejadian. Ini akan membantu petugas menemukan lokasi dengan mudah.</p>

                <!-- Map Container -->
                <div id="map"
                    class="w-full h-64 sm:h-80 lg:h-96 border border-gray-300 dark:border-gray-600 rounded-lg mb-3 bg-gray-100 dark:bg-gray-600">
                </div>

                <!-- Coordinate Display -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                        <label for="latitude"
                            class="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Latitude</label>
                        <input type="text" name="latitude" id="latitude"
                            class="w-full border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100 rounded-lg p-2 sm:p-3 text-xs sm:text-sm"
                            readonly placeholder="Akan terisi otomatis">
                    </div>
                    <div>
                        <label for="longitude"
                            class="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Longitude</label>
                        <input type="text" name="longitude" id="longitude"
                            class="w-full border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100 rounded-lg p-2 sm:p-3 text-xs sm:text-sm"
                            readonly placeholder="Akan terisi otomatis">
                    </div>
                </div>

                <!-- Location Info -->
                <div id="location-info" class="mt-3 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg hidden">
                    <p class="text-xs sm:text-sm text-blue-800 dark:text-blue-300">
                        <span class="font-semibold">📍 Lokasi terpilih:</span>
                        <span id="selected-location">-</span>
                    </p>
                </div>
            </div>

            <!-- Google Maps API will be loaded in the scripts section below -->

            <script>
                let map;
                let marker;

                function initMap() {
                    // Atur posisi awal peta (misal Bandung)
                    const defaultLocation = {
                        lat: -6.914744,
                        lng: 107.609810
                    };

                    map = new google.maps.Map(document.getElementById("map"), {
                        center: defaultLocation,
                        zoom: 13,
                    });

                    // Event klik pada peta
                    map.addListener("click", (e) => {
                        const clickedLocation = e.latLng;

                        // Jika marker sudah ada, pindahkan. Jika belum, buat marker.
                        if (marker) {
                            marker.setPosition(clickedLocation);
                        } else {
                            marker = new google.maps.Marker({
                                position: clickedLocation,
                                map: map,
                                animation: google.maps.Animation.DROP,
                            });
                        }

                        // Update koordinat ke input
                        document.getElementById("latitude").value = clickedLocation.lat().toFixed(6);
                        document.getElementById("longitude").value = clickedLocation.lng().toFixed(6);

                        // Tampilkan info lokasi
                        document.getElementById("selected-location").textContent =
                            `${clickedLocation.lat().toFixed(6)}, ${clickedLocation.lng().toFixed(6)}`;
                        document.getElementById("location-info").classList.remove("hidden");
                    });
                }

                // Panggil fungsi setelah halaman selesai dimuat
                window.addEventListener("load", initMap);
            </script>

            <div>
                <label for="isi"
                    class="block font-semibold text-gray-700 dark:text-gray-300 mb-2 text-sm sm:text-base">Isi
                    Laporan</label>
                <textarea name="isi" id="isi" rows="5"
                    class="w-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors duration-300 text-sm sm:text-base"
                    placeholder="Jelaskan detail masalah yang ingin dilaporkan..." required></textarea>
            </div>

            <div>
                <label for="bukti"
                    class="block font-semibold text-gray-700 dark:text-gray-300 mb-2 text-sm sm:text-base">Upload Bukti
                    (opsional)</label>
                <div class="relative">
                    <input type="file" name="bukti" id="bukti" accept="image/*,video/*"
                        class="w-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors duration-300 text-sm sm:text-base file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                </div>
                <div class="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                    <p class="text-xs sm:text-sm text-yellow-800 dark:text-yellow-300">
                        <span class="font-semibold">📎 Info Upload:</span><br>
                        • Format yang didukung: <strong>gambar (.jpg, .jpeg, .png)</strong> atau <strong>video (.mp4,
                            .mov)</strong><br>
                        • Maksimal ukuran: <strong>20MB</strong><br>
                        • Bukti foto/video akan membantu petugas memahami masalah dengan lebih baik
                    </p>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-3 pt-4">
                <button type="submit"
                    class="flex-1 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg shadow transition duration-200 text-sm sm:text-base">
                    📤 Kirim Laporan
                </button>
                <a href="<?php echo e(route('user.dashboard')); ?>"
                    class="flex-1 sm:flex-none bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-lg shadow transition duration-200 text-center text-sm sm:text-base">
                    ↩️ Kembali
                </a>
            </div>
        </form>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <!-- Check if Google Maps API key is configured -->
    <?php if(env('GOOGLE_MAPS_API_KEY') && env('GOOGLE_MAPS_API_KEY') !== 'YOUR_GOOGLE_MAPS_API_KEY_HERE'): ?>
        <!-- Google Maps JavaScript API -->
        <script async defer
            src="https://maps.googleapis.com/maps/api/js?key=<?php echo e(env('GOOGLE_MAPS_API_KEY')); ?>&callback=initMap&libraries=geometry"
            onerror="handleGoogleMapsError()"></script>
    <?php else: ?>
        <!-- No valid API key, show fallback immediately -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                showMapError();
            });
        </script>
    <?php endif; ?>

    <script>
        let map;
        let marker;
        let geocoder;

        // Error handling for Google Maps API
        function handleGoogleMapsError() {
            console.error('Google Maps API failed to load');
            showMapError();
        }

        function showMapError() {
            const mapContainer = document.getElementById('map');
            if (mapContainer) {
                mapContainer.innerHTML = `
            <div class="flex items-center justify-center h-full bg-blue-50 border-2 border-blue-200 rounded-lg">
                <div class="text-center p-6">
                    <svg class="w-16 h-16 text-blue-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-blue-800 mb-3">📍 Pilih Lokasi</h3>
                    <p class="text-blue-600 text-sm mb-4 max-w-sm mx-auto">
                        Google Maps belum dikonfigurasi. Silakan masukkan koordinat lokasi secara manual atau gunakan GPS ponsel Anda.
                    </p>
                    <div class="space-y-2">
                        <button onclick="enableManualLocation()" class="block w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-medium transition">
                            📝 Input Koordinat Manual
                        </button>
                        <button onclick="getCurrentLocation()" class="block w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm font-medium transition">
                            📱 Gunakan Lokasi Saat Ini
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-3">
                        💡 Tip: Anda bisa mendapatkan koordinat dari Google Maps di ponsel
                    </p>
                </div>
            </div>
        `;
            }
        }

        function enableManualLocation() {
            document.getElementById('latitude').readOnly = false;
            document.getElementById('longitude').readOnly = false;
            document.getElementById('latitude').placeholder = 'Contoh: -6.2088';
            document.getElementById('longitude').placeholder = 'Contoh: 106.8456';
            document.getElementById('latitude').classList.remove('bg-gray-50');
            document.getElementById('longitude').classList.remove('bg-gray-50');
            document.getElementById('latitude').classList.add('border-blue-300', 'focus:border-blue-500');
            document.getElementById('longitude').classList.add('border-blue-300', 'focus:border-blue-500');

            // Show instruction
            const locationInfo = document.getElementById('location-info');
            locationInfo.classList.remove('hidden');
            locationInfo.classList.remove('bg-blue-50');
            locationInfo.classList.add('bg-green-50');
            document.getElementById('selected-location').innerHTML = `
        <span class="text-green-800">✅ Input manual diaktifkan!</span><br>
        <span class="text-sm text-green-600">Masukkan koordinat latitude dan longitude, atau gunakan lokasi saat ini.</span>
    `;
        }

        function getCurrentLocation() {
            if (navigator.geolocation) {
                // Show loading
                const locationInfo = document.getElementById('location-info');
                locationInfo.classList.remove('hidden');
                locationInfo.classList.remove('bg-blue-50');
                locationInfo.classList.add('bg-yellow-50');
                document.getElementById('selected-location').innerHTML = `
            <span class="text-yellow-800">📍 Mencari lokasi Anda...</span><br>
            <span class="text-sm text-yellow-600">Mohon izinkan akses lokasi di browser.</span>
        `;

                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;

                        // Fill coordinates
                        document.getElementById('latitude').value = lat.toFixed(8);
                        document.getElementById('longitude').value = lng.toFixed(8);

                        // Enable manual editing
                        enableManualLocation();

                        // Show success
                        locationInfo.classList.remove('bg-yellow-50');
                        locationInfo.classList.add('bg-green-50');
                        document.getElementById('selected-location').innerHTML = `
                    <span class="text-green-800">✅ Lokasi berhasil didapatkan!</span><br>
                    <span class="text-sm text-green-600">Lat: ${lat.toFixed(6)}, Lng: ${lng.toFixed(6)}</span><br>
                    <span class="text-xs text-gray-500">Anda dapat mengedit koordinat jika diperlukan.</span>
                `;
                    },
                    function(error) {
                        console.error('Geolocation error:', error);

                        // Show error and enable manual input
                        enableManualLocation();

                        const locationInfo = document.getElementById('location-info');
                        locationInfo.classList.remove('bg-yellow-50');
                        locationInfo.classList.add('bg-red-50');
                        document.getElementById('selected-location').innerHTML = `
                    <span class="text-red-800">❌ Tidak dapat mengakses lokasi</span><br>
                    <span class="text-sm text-red-600">Silakan masukkan koordinat secara manual.</span>
                `;
                    }, {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 60000
                    }
                );
            } else {
                // Geolocation not supported
                enableManualLocation();

                const locationInfo = document.getElementById('location-info');
                locationInfo.classList.remove('hidden', 'bg-blue-50');
                locationInfo.classList.add('bg-red-50');
                document.getElementById('selected-location').innerHTML = `
            <span class="text-red-800">❌ Browser tidak mendukung geolocation</span><br>
            <span class="text-sm text-red-600">Silakan masukkan koordinat secara manual.</span>
        `;
            }
        }

        function initMap() {
            try {
                // Check if Google Maps is available
                if (typeof google === 'undefined' || !google.maps) {
                    throw new Error('Google Maps API not loaded');
                }

                // Default location (Jakarta, Indonesia)
                const defaultLocation = {
                    lat: -6.2088,
                    lng: 106.8456
                };

                // Initialize map
                map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 13,
                    center: defaultLocation,
                    mapTypeControl: true,
                    streetViewControl: true,
                    fullscreenControl: true,
                });
            } catch (error) {
                console.error('Error initializing map:', error);
                showMapError();
                return;
            }

            // Initialize geocoder
            geocoder = new google.maps.Geocoder();

            // Try to get user's current location
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const userLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };
                        map.setCenter(userLocation);
                        map.setZoom(16);

                        // Add marker at user's location
                        addMarker(userLocation);
                        updateLocationInfo(userLocation);
                    },
                    () => {
                        console.log('Geolocation failed, using default location');
                    }
                );
            }

            // Add click listener to map
            map.addListener('click', (event) => {
                const clickedLocation = {
                    lat: event.latLng.lat(),
                    lng: event.latLng.lng()
                };

                addMarker(clickedLocation);
                updateLocationInfo(clickedLocation);
            });
        }

        function addMarker(location) {
            // Remove existing marker
            if (marker) {
                marker.setMap(null);
            }

            // Add new marker
            marker = new google.maps.Marker({
                position: location,
                map: map,
                title: 'Lokasi Laporan',
                animation: google.maps.Animation.DROP,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="#dc2626">
                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
            `),
                    scaledSize: new google.maps.Size(32, 32),
                    anchor: new google.maps.Point(16, 32)
                }
            });

            // Update coordinate inputs
            document.getElementById('latitude').value = location.lat.toFixed(8);
            document.getElementById('longitude').value = location.lng.toFixed(8);

            // Get address from coordinates
            geocoder.geocode({
                location: location
            }, (results, status) => {
                if (status === 'OK' && results[0]) {
                    const address = results[0].formatted_address;
                    document.getElementById('selected-location').textContent = address;
                    document.getElementById('location-info').classList.remove('hidden');
                }
            });
        }

        function updateLocationInfo(location) {
            const lat = location.lat.toFixed(6);
            const lng = location.lng.toFixed(6);

            // Show location info
            document.getElementById('location-info').classList.remove('hidden');

            // Update coordinate display
            document.getElementById('latitude').value = location.lat.toFixed(8);
            document.getElementById('longitude').value = location.lng.toFixed(8);
        }

        // Handle form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            const lat = document.getElementById('latitude').value;
            const lng = document.getElementById('longitude').value;

            if (!lat || !lng) {
                e.preventDefault();
                alert('Silakan pilih lokasi di peta atau masukkan koordinat secara manual!');
                return false;
            }

            // Validate coordinate ranges
            const latitude = parseFloat(lat);
            const longitude = parseFloat(lng);

            if (isNaN(latitude) || isNaN(longitude)) {
                e.preventDefault();
                alert('Koordinat harus berupa angka yang valid!');
                return false;
            }

            if (latitude < -90 || latitude > 90) {
                e.preventDefault();
                alert('Latitude harus antara -90 dan 90!');
                return false;
            }

            if (longitude < -180 || longitude > 180) {
                e.preventDefault();
                alert('Longitude harus antara -180 dan 180!');
                return false;
            }
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('dashboard.template', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\web\LaporinAja\resources\views/laporan/create.blade.php ENDPATH**/ ?>